import 'dart:developer';
import 'dart:io';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:platix/controllers/dentist_controllers/dentist_profile_controller.dart';
import 'package:platix/controllers/signin_controller.dart';
import 'package:platix/services/permission_service.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/screens/dentist/dentist_editprofilescreen.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../api/data_store.dart';
import '../../../data/models/user_model.dart';
import '../../../utils/onesignal_helper.dart';
import '../../../utils/web_responsive_utils.dart';
import '../signinOption_screen.dart';
import 'dentist_homescreen.dart';
import 'dentist_orders_screen.dart';
import 'dentist_privacypolicy_screen.dart';
import 'dentist_reports_screen.dart';
import '../../../bindings/get_di.dart' as di;
import 'dentist_t&c_screen.dart';

class DentistProfilescreen extends StatefulWidget {
  const DentistProfilescreen({super.key});

  @override
  State<DentistProfilescreen> createState() => _DentistProfilescreenState();
}

class _DentistProfilescreenState extends State<DentistProfilescreen> {
  String? path;

  Uint8List? _selectedImage;
  String? userProfileImage; // Stores profile image path or URL
  UserRecord? user;

  bool isLoading=false;

  ProfileController profileController=Get.put(ProfileController());

  @override
  void initState() {
    super.initState();
    _loadUserProfile();
  }

  void _loadUserProfile() {
    var storedUser = GetStorage().read('userRecord');

    if (storedUser == null) {
      log("❌ Error: No user data found in GetStorage.");
      return;
    }

    if (storedUser is! Map<String, dynamic>) {
      log("❌ Error: Expected Map<String, dynamic> but got ${storedUser.runtimeType}");
      return;
    }

    user = UserRecord.fromJson(storedUser);

    userProfileImage = user?.profileImage;

    log("🔍 Loaded profile image path: $userProfileImage");

    if (userProfileImage != null && userProfileImage!.isNotEmpty) {
      if (userProfileImage!.startsWith("http")) {
        log("🌐 Detected network image.");
        setState(() {
          _selectedImage = null; // So the UI builder loads it from network
        });
      } else {
        File imageFile = File(userProfileImage!);
        if (imageFile.existsSync()) {
          log("📁 Local image file exists.");
          setState(() {
            _selectedImage = imageFile.readAsBytesSync();
          });
        } else {
          log("❌ Local image file not found.");
        }
      }
    }
  }


  @override
  Widget build(BuildContext context) {
    final PermissionService permissionService = PermissionService();
    return Visibility(
      visible: permissionService.hasAnyPermission('dentists', ['is_view', 'is_list']),
      child: Scaffold(
      backgroundColor: Colors.white,
      appBar:
      kIsWeb
          ?WebResponsiveUtils.dentistWebAppBar(3,context)
      :const CustomAppBar(
        title: "Profile",
        centerTitle: true,
        backgroundColor: AppColors.primary,
        textColor: Colors.white,
        leadingBack: false,
      ),
      drawer: (MediaQuery.of(context).size.width <= 600 && kIsWeb)
          ? Drawer(
        child: ListView(
          children: [
            DrawerHeader(
              decoration: const BoxDecoration(color: AppColors.primary),
              child: CustomImageView(
                fit: BoxFit.none,
                imagePath: AppIcons.appLogo,
                color: AppColors.white,
              ),
            ),
            ListTile(
              leading: CustomImageView(
                imagePath: AppIcons.home,
              ),
              title: const Text('Home'),
              onTap: () {
                Get.offAll(() => const DentistHomeScreen());
              },
            ),
            ListTile(
              //leading: Icon(AppIcons.orders),
              leading: CustomImageView(
                imagePath: AppIcons.orders,
              ),
              title: const Text('Order'),
              onTap: () {
                Get.offAll(() =>  DentistOrdersScreen());
              },
            ),
            ListTile(
              //leading: Icon(AppIcons.orders),
              leading: CustomImageView(
                imagePath: AppIcons.reports,
              ),
              title: const Text('Reports'),
              onTap: () {
                Get.offAll(() => const DentistReportsScreen());
              },
            ),
            ListTile(
              //leading: Icon(AppIcons.orders),
              leading: CustomImageView(
                imagePath: AppIcons.profile,
              ),
              title: const Text('Profile'),
              onTap: () {
                Get.offAll(() => const DentistProfilescreen());
              },
            ),

          ],
        ),
      )
          : null,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Center(
            child: Container(
              color: Colors.white,
              width: double.infinity,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: AppSizes.defaultSpace,),
                    Center(
                      child: SizedBox(
                        height: 100,
                        width: 100,
                        child: Stack(
                          children: [
                            InkWell(
                              onTap: () {
                               // _openGallery(context); // Open the gallery on tap
                              },
                              child: Center(
                                child: Container(
                                  height: 100,
                                  width: 100,
                                  decoration: BoxDecoration(
                                    border: Border.all(color: AppColors.primary, width: 1.6), // Purple border
                                    shape: BoxShape.circle,
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(80),
                                    child: Builder(
                                      builder: (_) {
                                        if (_selectedImage != null) {
                                          return Image.memory(
                                            _selectedImage!,
                                            fit: BoxFit.cover,
                                            key: const ValueKey("memory"),
                                          );
                                        }

                                        if (userProfileImage != null && userProfileImage!.isNotEmpty) {
                                          if (userProfileImage!.startsWith("http")) {
                                            return Image.network(
                                              userProfileImage!,
                                              fit: BoxFit.cover,
                                              key: const ValueKey("network"),
                                              loadingBuilder: (context, child, loadingProgress) {
                                                if (loadingProgress == null) return child;
                                                return const Center(child: CircularProgressIndicator(strokeWidth: 2));
                                              },
                                              errorBuilder: (_, __, ___) => Image.asset(AppImages.test, fit: BoxFit.cover),
                                            );
                                          } else {
                                            final file = File(userProfileImage!);
                                            if (file.existsSync()) {
                                              return Image.file(
                                                file,
                                                fit: BoxFit.cover,
                                                key: const ValueKey("file"),
                                              );
                                            }
                                          }
                                        }

                                        return Image.asset(AppImages.test, fit: BoxFit.cover);
                                      },
                                    ),

                                  ),
                                ),
                              ),
                            ),
                            // Positioned(
                            // bottom: 8,
                            //   right: 2,
                            //   child: InkWell(
                            //     onTap: () {
                            //       _openGallery(context); // Open the gallery on tap
                            //     },
                            //     child:  Container(
                            //       decoration: BoxDecoration(
                            //           borderRadius: BorderRadius.circular(20),
                            //           border: Border.all(color: AppColors.primary, width: 1.6)
                            //       ),
                            //       height: 24,
                            //       width: 24,
                            //       child: CircleAvatar(
                            //         backgroundColor: Colors.white,
                            //         radius: 20, // Inner size
                            //         child: CustomImageView(
                            //           imagePath: AppIcons.add,
                            //           color: Colors.purple,
                            //           height: 16,
                            //           width: 16,
                            //         ),
                            //       ),
                            //     ),
                            //   ),
                            // ),

                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: AppSizes.spaceBtwSections,),
                    Visibility(
                      visible: permissionService.hasPermission('dentists', 'is_edit'),
                      child: settingWidget(
                          name: "Edit Profile",
                          imagePath: AppIcons.editprofile,
                          onTap:() async{
                           await Get.to(() => DentistEditProfilescreen());
                          }
                      ),
                    ),
                    // const SizedBox(height: AppSizes.spaceBtwItems,),
                    // settingWidget(
                    //   name: "Language",
                    //   imagePath: AppIcons.language,
                    // ),
                    const SizedBox(height: AppSizes.spaceBtwItems,),
                    settingWidget(
                        name: "Terms & Conditions",
                        imagePath: AppIcons.termsandcond,
                        useSpecialIcon: false,
                      onTap: (){
                          Get.to(() =>TermsConditionsScreen());
                      }

                    ),
                    const SizedBox(height: AppSizes.spaceBtwItems,),
                    settingWidget(
                        name: "Privacy Policy",
                        imagePath: AppIcons.privacy,
                        useSpecialIcon: false,
                      onTap: (){
                          Get.to(() =>PrivacyPolicyScreen());

                      }

                    ),
                    const SizedBox(height: AppSizes.spaceBtwItems,),
                    settingWidget(
                        name: "Contact Us",
                        imagePath: AppIcons.contact,
                        onTap: (){
                          Get.toNamed(AppRoutes.contactUsScreen);
                        },
                        useSpecialIcon: false
                    ),
                    const SizedBox(height: AppSizes.spaceBtwItems,),
                    settingWidget(
                        name: "Refunds and Cancellations",
                        imagePath: AppIcons.refund,
                        onTap: () {
                          Get.toNamed('/refund-policy');
                        },
                        useSpecialIcon: false
                    ),
                    const SizedBox(height: AppSizes.spaceBtwItems,),
                    settingWidget(
                        name: "Delete Account",
                        imagePath: AppIcons.delete,
                        onTap: (){
                          MediaQuery.of(context).size.width > 600 ? deleteSheetWeb(context) : deleteSheet();
                        },
                        useSpecialIcon: false
                    ),
                    const SizedBox(height: AppSizes.spaceBtwItems,),
                    settingWidget(
                        name: "Logout",
                        imagePath: AppIcons.logout,
                        onTap: (){
                          MediaQuery.of(context).size.width > 600 ? logoutSheetWeb(context) : logoutSheet();
                        },
                        useSpecialIcon: false
                    ),
                    const SizedBox(height: AppSizes.spaceBtwItems,),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    ),
    );
  }

  Widget settingWidget({Function()? onTap, String? name, String? imagePath,bool useSpecialIcon = true,}) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: AppSizes.md),
        height: 56,
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(8), color: Colors.white, boxShadow: const [BoxShadow(blurRadius: 3, color: AppColors.grey)]),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            CustomImageView(
              imagePath: imagePath,
            ),
            const SizedBox(
              width: AppSizes.spaceBtwList,
            ),
            Text(
              name ?? "",
              style: CustomTextStyles.b2_1,
            ),
            const Spacer(),
            useSpecialIcon ?
            CustomImageView(
              imagePath: AppIcons.arrowForward,
              color: AppColors.black,
            ): const SizedBox(),

          ],
        ),
      ),
    );
  }

  Future deleteSheet() {
    ProfileController profileController=Get.find<ProfileController>();
    return Get.bottomSheet(
      Container(
        height: 262,
        width: MediaQuery.of(context).size.width > 600 ? MediaQuery.of(context).size.width*0.8 : Get.size.width,
        decoration: const BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(30),
            topRight: Radius.circular(30),
          ),
        ),
        child: Column(
          children: [
            const SizedBox(
              height: AppSizes.defaultSpace,
            ),
            Text(
              "Delete Account",
              style: CustomTextStyles.h6.copyWith(color: Colors.black),
            ),
            const SizedBox(
              height: 6,
            ),
            const Padding(
              padding: EdgeInsets.symmetric(
                horizontal: 125,
              ),
              child: Divider(color: Colors.black),
            ),
            const SizedBox(
              height: AppSizes.defaultSpace,
            ),
            Text(
              "Are you sure you want to Delete",
              style: CustomTextStyles.b2_1,
            ),
            Center(
              child: Text(
                "account ?",
                style: CustomTextStyles.b2_1,
              ),
            ),
            const SizedBox(
              height: AppSizes.defaultSpace,
            ),
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () {
                      Get.back();
                    },
                    child: Container(
                      height: 56,
                      margin:
                      const EdgeInsets.symmetric(horizontal: AppSizes.md),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: AppColors.primary2,
                        borderRadius:
                        BorderRadius.circular(AppSizes.borderRadiusMd),
                      ),
                      child: Text(
                        "Cancel",
                        style: CustomTextStyles.b3_primary,
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: InkWell(
                    onTap: () {
                      profileController.deleteAccount();
                      // Get.off(() => const SigninOptionScreen());
                    },
                    child: Container(
                      height: 56,
                      margin: const EdgeInsets.only(right: AppSizes.md),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius:
                        BorderRadius.circular(AppSizes.borderRadiusMd),
                      ),
                      child: Text(
                        "Yes,Remove",
                        style: CustomTextStyles.b3_1,
                      ),
                    ),
                  ),
                )
              ],
            )
          ],
        ),
      ),
    );
  }

  Future logoutSheet() {
    return Get.bottomSheet(
      Container(
        height: 262,
        width: MediaQuery.of(context).size.width > 600 ? Get.size.width* 0.8 : double.infinity,
        decoration: const BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(30),
            topRight: Radius.circular(30),
          ),
        ),
        child: Column(
          children: [
            const SizedBox(
              height: AppSizes.defaultSpace,
            ),
            Text(
              "Logout",
              style: CustomTextStyles.h6,
            ),
            const SizedBox(
              height: AppSizes.spaceBtwList,
            ),
            Container(
              height: 1,
              width: 90,
              color: Colors.black,
            ),
            const SizedBox(
              height: AppSizes.defaultSpace,
            ),
            Text(
              "Are you sure you want to log out ?",
              style: CustomTextStyles.b2_1,

            ),
            const SizedBox(
              height: AppSizes.defaultSpace,
            ),

            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () async {
                      Get.back();
                    },
                    child: Container(
                      height: 56,
                      margin: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: AppColors.primary2,
                        borderRadius: BorderRadius.circular(AppSizes.borderRadiusMd),
                      ),
                      child: Text(
                        "Cancel",
                        style: CustomTextStyles.b3_primary,

                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: isLoading ? const Center(child: CircularProgressIndicator(),) : InkWell(
                    onTap: () async {
                      setState(() {
                        isLoading = true;
                      });
                      final signinController = Get.put(SignInController());
                      final subId = await OneSignalHelper.getCurrentUserSubId();
                      await signinController.removeOneSignalSubId(subId);
                      getData.remove('token');
                      getData.remove('userRecord');
                      getData.remove('profileImage');
                      FirebaseAuth.instance.signOut();
                      final prefs = await SharedPreferences.getInstance();
                      await prefs.setBool('termsAccepted', false);
                      Get.off(() => const SigninOptionScreen());
                      await di.init();
                      setState(() {
                        isLoading = false;
                      });
                    },
                    child: Container(
                      height: 56,
                      margin: const EdgeInsets.only(right: AppSizes.md),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(AppSizes.borderRadiusMd),
                      ),
                      child: Text(
                        "Yes,Logout",
                        style: CustomTextStyles.b3_1,

                      ),
                    ),
                  ),
                )
              ],
            )
          ],
        ),
      ),
    );
  }

  Future logoutSheetWeb(BuildContext context) {
    return showDialog(
      context: context,
      barrierDismissible: true, // User must tap button
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(30),
          ),
          child: Container(
            // height: 280, // Slightly more than 262 for better spacing
            width: MediaQuery.of(context).size.width * 0.4, // Not full screen
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(30),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: AppSizes.defaultSpace),
                Text(
                  "Logout",
                  style: CustomTextStyles.h6,
                ),
                const SizedBox(height: AppSizes.spaceBtwList),
                Container(
                  width: 100,
                  height: 1.5,
                  color: Colors.black,),
                const SizedBox(height: AppSizes.defaultSpace),
                Text(
                  "Are you sure you want to log out ?",
                  textAlign: TextAlign.center,
                  style: CustomTextStyles.b2_1,
                ),
                const SizedBox(height: AppSizes.defaultSpace),
                Row(
                  children: [
                    Expanded(
                      child: InkWell(
                        onTap: () {
                          Navigator.of(context).pop(); // Close the dialog
                        },
                        child: Container(
                          height: 56,
                          margin: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: AppColors.primary2,
                            borderRadius: BorderRadius.circular(AppSizes.borderRadiusMd),
                          ),
                          child: Text(
                            "Cancel",
                            style: CustomTextStyles.b3_primary,
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child:isLoading ? const Center(child: CircularProgressIndicator(),) : InkWell(
                        onTap: () async {
                          setState(() {
                            isLoading = true;
                          });
                          final signinController = Get.put(SignInController());
                          final subId = await OneSignalHelper.getCurrentUserSubId();
                          await signinController.removeOneSignalSubId(subId);
                          getData.remove('token');
                          getData.remove('userRecord');
                          getData.remove('profileImage');
                          FirebaseAuth.instance.signOut();
                          final prefs = await SharedPreferences.getInstance();
                          await prefs.setBool('termsAccepted', false);
                          Get.off(() => const SigninOptionScreen());
                          await di.init();
                          setState(() {
                            isLoading = false;
                          });
                        },
                        child: Container(
                          height: 56,
                          margin: const EdgeInsets.only(right: AppSizes.md),
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            borderRadius: BorderRadius.circular(AppSizes.borderRadiusMd),
                          ),
                          child: Text(
                            "Yes, Logout",
                            style: CustomTextStyles.b3_1,
                          ),
                        ),
                      ),
                    )
                  ],
                ),
                const SizedBox(height: AppSizes.defaultSpace),
              ],
            ),
          ),
        );
      },
    );
  }

  Future deleteSheetWeb(BuildContext context) {
    return showDialog(
      context: context,
      barrierDismissible: true, // User must tap button
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(30),
          ),
          child: Container(
            // height: 280, // Slightly more than 262 for better spacing
            width: MediaQuery.of(context).size.width * 0.4, // Not full screen
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(30),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: AppSizes.defaultSpace),
                Text(
                  "Delete Account",
                  style: CustomTextStyles.h6,
                ),
                const SizedBox(height: AppSizes.spaceBtwList),
                Container(
                  width: 170,
                  height: 1.5,
                  color: Colors.black,),
                const SizedBox(height: AppSizes.defaultSpace),
                Text(
                  "Are you sure you want to Delete account ?",
                  textAlign: TextAlign.center,
                  style: CustomTextStyles.b2_1,
                ),
                const SizedBox(height: AppSizes.defaultSpace),
                Row(
                  children: [
                    Expanded(
                      child: InkWell(
                        onTap: () {
                          Navigator.of(context).pop(); // Close the dialog
                        },
                        child: Container(
                          height: 56,
                          margin: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: AppColors.primary2,
                            borderRadius: BorderRadius.circular(AppSizes.borderRadiusMd),
                          ),
                          child: Text(
                            "Cancel",
                            style: CustomTextStyles.b3_primary,
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: InkWell(
                        onTap: () {
                          profileController.deleteAccount();
                          // Get.off(() => const SigninOptionScreen());
                        },
                        child: Container(
                          height: 56,
                          margin: const EdgeInsets.only(right: AppSizes.md),
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            borderRadius: BorderRadius.circular(AppSizes.borderRadiusMd),
                          ),
                          child: Text(
                            "Yes,Remove",
                            style: CustomTextStyles.b3_1,
                          ),
                        ),
                      ),
                    )
                  ],
                ),
                const SizedBox(height: AppSizes.defaultSpace),
              ],
            ),
          ),
        );
      },
    );
  }
}
