import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/services/permission_service.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/utils/constants/colors.dart';
import 'package:platix/view/screens/dentist/create_patient_registration_screen.dart';

class PatientRegistrationScreen extends StatelessWidget {
  const PatientRegistrationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final PermissionService permissionService = PermissionService();
    return Visibility(
      visible: permissionService.hasAnyPermission('Registration', ['is_view', 'is_list', 'is_add']),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Patient Registration'),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Patient Registration List',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 20),
                Visibility(
                  visible: permissionService.hasPermission('Registration', 'is_list'),
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: DataTable(
                      columnSpacing: 10,
                      horizontalMargin: 10,
                      headingRowColor: WidgetStateProperty.all(AppColors.primary),
                      headingTextStyle: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                      columns: const [
                        DataColumn(label: Text('S.No')),
                        DataColumn(label: Text('Patient Name')),
                        DataColumn(label: Text('Age/Gender')),
                        DataColumn(label: Text('Registration Date')),
                        DataColumn(label: Text('Edit')),
                        DataColumn(label: Text('Delete')),
                      ],
                      rows: [
                        DataRow(
                          color: WidgetStateProperty.all(AppColors.primary2),
                          cells: [
                            const DataCell(Text('1')),
                            const DataCell(Text('John Smith')),
                            const DataCell(Text('35/M')),
                            const DataCell(Text('15/01/2024')),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('Registration', 'is_edit'),
                                child: IconButton(
                                  icon: const Icon(Icons.edit, color: AppColors.primary),
                                  onPressed: () {
                                    Get.to(() => const CreatePatientRegistrationScreen(showSearchOption: false));
                                  },
                                ),
                              ),
                            ),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('Registration', 'is_delete'),
                                child: IconButton(
                                  icon: const Icon(Icons.delete, color: AppColors.primary),
                                  onPressed: () {
                                    Get.defaultDialog(
                                      title: 'Delete Patient',
                                      titleStyle: const TextStyle(
                                        color: AppColors.primary,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      middleText: 'Are you sure you want to delete patient John Smith?',
                                      middleTextStyle: const TextStyle(
                                        color: AppColors.black,
                                        fontSize: 14,
                                      ),
                                      backgroundColor: AppColors.white,
                                      radius: 12,
                                      textConfirm: 'Delete',
                                      textCancel: 'Cancel',
                                      confirmTextColor: Colors.white,
                                      cancelTextColor: AppColors.primary,
                                      buttonColor: AppColors.primary,
                                      onConfirm: () {
                                        Get.back();
                                        Get.snackbar('Success', 'Patient John Smith deleted successfully!');
                                      },
                                      onCancel: () {
                                        Get.back();
                                      },
                                    );
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                        DataRow(
                          color: WidgetStateProperty.all(Colors.white),
                          cells: [
                            const DataCell(Text('2')),
                            const DataCell(Text('Sarah Johnson')),
                            const DataCell(Text('28/F')),
                            const DataCell(Text('18/01/2024')),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('Registration', 'is_edit'),
                                child: IconButton(
                                  icon: const Icon(Icons.edit, color: AppColors.primary),
                                  onPressed: () {
                                    Get.to(() => const CreatePatientRegistrationScreen(showSearchOption: false));
                                  },
                                ),
                              ),
                            ),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('Registration', 'is_delete'),
                                child: IconButton(
                                  icon: const Icon(Icons.delete, color: AppColors.primary),
                                  onPressed: () {
                                    Get.defaultDialog(
                                      title: 'Delete Patient',
                                      titleStyle: const TextStyle(
                                        color: AppColors.primary,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      middleText: 'Are you sure you want to delete patient Sarah Johnson?',
                                      middleTextStyle: const TextStyle(
                                        color: AppColors.black,
                                        fontSize: 14,
                                      ),
                                      backgroundColor: AppColors.white,
                                      radius: 12,
                                      textConfirm: 'Delete',
                                      textCancel: 'Cancel',
                                      confirmTextColor: Colors.white,
                                      cancelTextColor: AppColors.primary,
                                      buttonColor: AppColors.primary,
                                      onConfirm: () {
                                        Get.back();
                                        Get.snackbar('Success', 'Patient Sarah Johnson deleted successfully!');
                                      },
                                      onCancel: () {
                                        Get.back();
                                      },
                                    );
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                        DataRow(
                          color: WidgetStateProperty.all(AppColors.primary2),
                          cells: [
                            const DataCell(Text('3')),
                            const DataCell(Text('Michael Brown')),
                            const DataCell(Text('42/M')),
                            const DataCell(Text('22/01/2024')),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('Registration', 'is_edit'),
                                child: IconButton(
                                  icon: const Icon(Icons.edit, color: AppColors.primary),
                                  onPressed: () {
                                    Get.to(() => const CreatePatientRegistrationScreen(showSearchOption: false));
                                  },
                                ),
                              ),
                            ),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('Registration', 'is_delete'),
                                child: IconButton(
                                  icon: const Icon(Icons.delete, color: AppColors.primary),
                                  onPressed: () {
                                    Get.defaultDialog(
                                      title: 'Delete Patient',
                                      titleStyle: const TextStyle(
                                        color: AppColors.primary,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      middleText: 'Are you sure you want to delete patient Michael Brown?',
                                      middleTextStyle: const TextStyle(
                                        color: AppColors.black,
                                        fontSize: 14,
                                      ),
                                      backgroundColor: AppColors.white,
                                      radius: 12,
                                      textConfirm: 'Delete',
                                      textCancel: 'Cancel',
                                      confirmTextColor: Colors.white,
                                      cancelTextColor: AppColors.primary,
                                      buttonColor: AppColors.primary,
                                      onConfirm: () {
                                        Get.back();
                                        Get.snackbar('Success', 'Patient Michael Brown deleted successfully!');
                                      },
                                      onCancel: () {
                                        Get.back();
                                      },
                                    );
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                        DataRow(
                          color: WidgetStateProperty.all(Colors.white),
                          cells: [
                            const DataCell(Text('4')),
                            const DataCell(Text('Emily Davis')),
                            const DataCell(Text('31/F')),
                            const DataCell(Text('25/01/2024')),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('Registration', 'is_edit'),
                                child: IconButton(
                                  icon: const Icon(Icons.edit, color: AppColors.primary),
                                  onPressed: () {
                                    Get.to(() => const CreatePatientRegistrationScreen(showSearchOption: false));
                                  },
                                ),
                              ),
                            ),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('Registration', 'is_delete'),
                                child: IconButton(
                                  icon: const Icon(Icons.delete, color: AppColors.primary),
                                  onPressed: () {
                                    Get.defaultDialog(
                                      title: 'Delete Patient',
                                      titleStyle: const TextStyle(
                                        color: AppColors.primary,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      middleText: 'Are you sure you want to delete patient Emily Davis?',
                                      middleTextStyle: const TextStyle(
                                        color: AppColors.black,
                                        fontSize: 14,
                                      ),
                                      backgroundColor: AppColors.white,
                                      radius: 12,
                                      textConfirm: 'Delete',
                                      textCancel: 'Cancel',
                                      confirmTextColor: Colors.white,
                                      cancelTextColor: AppColors.primary,
                                      buttonColor: AppColors.primary,
                                      onConfirm: () {
                                        Get.back();
                                        Get.snackbar('Success', 'Patient Emily Davis deleted successfully!');
                                      },
                                      onCancel: () {
                                        Get.back();
                                      },
                                    );
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                        DataRow(
                          color: WidgetStateProperty.all(AppColors.primary2),
                          cells: [
                            const DataCell(Text('5')),
                            const DataCell(Text('Robert Wilson')),
                            const DataCell(Text('55/M')),
                            const DataCell(Text('28/01/2024')),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('Registration', 'is_edit'),
                                child: IconButton(
                                  icon: const Icon(Icons.edit, color: AppColors.primary),
                                  onPressed: () {
                                    Get.to(() => const CreatePatientRegistrationScreen(showSearchOption: false));
                                  },
                                ),
                              ),
                            ),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('Registration', 'is_delete'),
                                child: IconButton(
                                  icon: const Icon(Icons.delete, color: AppColors.primary),
                                  onPressed: () {
                                    Get.defaultDialog(
                                      title: 'Delete Patient',
                                      titleStyle: const TextStyle(
                                        color: AppColors.primary,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      middleText: 'Are you sure you want to delete patient Robert Wilson?',
                                      middleTextStyle: const TextStyle(
                                        color: AppColors.black,
                                        fontSize: 14,
                                      ),
                                      backgroundColor: AppColors.white,
                                      radius: 12,
                                      textConfirm: 'Delete',
                                      textCancel: 'Cancel',
                                      confirmTextColor: Colors.white,
                                      cancelTextColor: AppColors.primary,
                                      buttonColor: AppColors.primary,
                                      onConfirm: () {
                                        Get.back();
                                        Get.snackbar('Success', 'Patient Robert Wilson deleted successfully!');
                                      },
                                      onCancel: () {
                                        Get.back();
                                      },
                                    );
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        floatingActionButton: Visibility(
          visible: permissionService.hasPermission('Registration', 'is_add'),
          child: FloatingActionButton(
            onPressed: () {
              Get.to(() => const CreatePatientRegistrationScreen(showSearchOption: true));
            },
            backgroundColor: AppColors.primary,
            child: const Icon(Icons.add, color: Colors.white),
          ),
        ),
      ),
    );
  }
}
