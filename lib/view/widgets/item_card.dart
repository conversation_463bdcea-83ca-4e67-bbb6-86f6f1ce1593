import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/controllers/cart/cart_controller.dart';
import 'package:platix/data/models/item_model.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/theme/animations.dart';

class ItemCard extends StatelessWidget {
  final Item item;
  final VoidCallback onTap;

  const ItemCard({
    super.key,
    required this.item,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final RxBool showQtySelector = false.obs;
    return M3InteractiveButton(
      onPressed: onTap,
      child: SizedBox(
        width: 250, // Further increased width to prevent overlap
        child: Stack(
          children: [
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadiusStyle.radius8,
                boxShadow: AppDecoration.shadow1_3,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 3,
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(8),
                          topRight: Radius.circular(8),
                        ),
                      ),
                      child: Stack(
                        children: [
                          ClipRRect(
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(8),
                              topRight: Radius.circular(8),
                            ),
                            child: CustomImageView(
                              imagePath: item.photoUrl.isNotEmpty ? item.photoUrl.first : null,
                              fit: BoxFit.cover,
                              width: double.infinity,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            item.name,
                            style: CustomTextStyles.b4_1,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '\u20B9${item.discountedPrice.toStringAsFixed(2)}',
                                style: CustomTextStyles.b4_1.copyWith(
                                  color: AppColors.primary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              if (item.discount > 0)
                                Text(
                                  '\u20B9${item.mrp.toStringAsFixed(2)}',
                                  style: CustomTextStyles.b4_1.copyWith(
                                    color: AppColors.darkGrey,
                                    decoration: TextDecoration.lineThrough,
                                    fontSize: 12,
                                  ),
                                ),
                            ],
                          ),
                          if (item.discount > 0)
                            Text(
                              'Save \u20B9${(item.mrp - item.discountedPrice).toStringAsFixed(2)}',
                              style: CustomTextStyles.b4_1.copyWith(
                                color: AppColors.success,
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Positioned(
              bottom: 12,
              right: 12,
              child: Obx(() {
                final cartController = Get.find<CartController>();
                int cartQuantity = cartController.getQuantity(item);
                if (item.quantity == 0) {
                  return Container();
                }
                return showQtySelector.value || cartQuantity > 0
                    ? Container(
                        height: 32,
                        width: 70,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(AppSizes.borderRadiusSm),
                          border: Border.all(color: AppColors.primary),
                        ),
                        child: Row(
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(left: AppSizes.sm, right: AppSizes.xs),
                              child: InkWell(
                                child: Icon(Icons.remove, color: AppColors.primary, size: 16),
                                onTap: () {
                                  if (cartQuantity > 0) {
                                    final cartItem = cartController.cartItems.firstWhereOrNull((cartItem) => cartItem.item.name == item.name);
                                    if (cartItem != null) {
                                      cartController.decrementQuantity(cartItem);
                                      if (cartController.getQuantity(item) == 0) {
                                        showQtySelector.value = false;
                                      }
                                    }
                                  }
                                },
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 1),
                              child: Text('$cartQuantity', style: CustomTextStyles.b6_3),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(right: AppSizes.xs, left: AppSizes.sm),
                              child: InkWell(
                                child: Icon(Icons.add, color: AppColors.primary, size: 16),
                                onTap: () {
                                  cartController.addToCart(item);
                                  showQtySelector.value = true;
                                },
                              ),
                            ),
                          ],
                        ),
                      )
                    : Container(
                        decoration: BoxDecoration(
                          color: AppColors.primary,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.shadow1,
                              blurRadius: 4,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: SizedBox(
                          width: 40,
                          height: 40,
                          child: IconButton(
                            icon: Icon(Icons.add_shopping_cart, color: AppColors.white, size: 16),
                            onPressed: () {
                              cartController.addToCart(item);
                              showQtySelector.value = true;
                            },
                          ),
                        ),
                      );
              }),
            ),
            if (item.quantity == 0)
              Container(
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.5),
                  borderRadius: BorderRadiusStyle.radius8,
                ),
                child: Center(
                  child: Text(
                    'Out of Stock',
                    style: CustomTextStyles.h6.copyWith(color: Colors.white),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
