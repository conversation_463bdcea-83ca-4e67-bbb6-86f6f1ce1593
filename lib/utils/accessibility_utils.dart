import 'package:flutter/material.dart';
import 'package:flutter/semantics.dart';

/// Accessibility utilities for M3 Expressive design
/// Ensures proper accessibility standards and usability
class AccessibilityUtils {
  AccessibilityUtils._();

  // Minimum touch target sizes following Material Design guidelines
  static const double minTouchTargetSize = 48.0;
  static const double recommendedTouchTargetSize = 56.0;
  static const double largeTouchTargetSize = 64.0;

  // Contrast ratios for accessibility compliance
  static const double minContrastRatio = 4.5; // WCAG AA standard
  static const double enhancedContrastRatio = 7.0; // WCAG AAA standard

  /// Ensures minimum touch target size for interactive elements
  static Widget ensureMinTouchTarget({
    required Widget child,
    double minSize = minTouchTargetSize,
    VoidCallback? onTap,
    String? semanticLabel,
    String? semanticHint,
    bool excludeSemantics = false,
  }) {
    Widget result = ConstrainedBox(
      constraints: BoxConstraints(
        minWidth: minSize,
        minHeight: minSize,
      ),
      child: child,
    );

    if (onTap != null) {
      result = GestureDetector(
        onTap: onTap,
        child: result,
      );
    }

    if (!excludeSemantics && (semanticLabel != null || semanticHint != null)) {
      result = Semantics(
        label: semanticLabel,
        hint: semanticHint,
        button: onTap != null,
        child: result,
      );
    }

    return result;
  }

  /// Creates an accessible button with proper semantics
  static Widget accessibleButton({
    required Widget child,
    required VoidCallback? onPressed,
    required String semanticLabel,
    String? semanticHint,
    bool enabled = true,
    double minTouchTarget = recommendedTouchTargetSize,
    EdgeInsetsGeometry? padding,
  }) {
    return Semantics(
      label: semanticLabel,
      hint: semanticHint,
      button: true,
      enabled: enabled,
      child: ConstrainedBox(
        constraints: BoxConstraints(
          minWidth: minTouchTarget,
          minHeight: minTouchTarget,
        ),
        child: Padding(
          padding: padding ?? EdgeInsets.zero,
          child: child,
        ),
      ),
    );
  }

  /// Creates accessible text with proper semantics
  static Widget accessibleText({
    required String text,
    TextStyle? style,
    String? semanticLabel,
    bool isHeading = false,
    bool isLiveRegion = false,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
  }) {
    return Semantics(
      label: semanticLabel ?? text,
      header: isHeading,
      liveRegion: isLiveRegion,
      child: Text(
        text,
        style: style,
        textAlign: textAlign,
        maxLines: maxLines,
        overflow: overflow,
      ),
    );
  }

  /// Creates an accessible icon with proper semantics
  static Widget accessibleIcon({
    required IconData icon,
    required String semanticLabel,
    Color? color,
    double? size,
    String? semanticHint,
  }) {
    return Semantics(
      label: semanticLabel,
      hint: semanticHint,
      image: true,
      child: Icon(
        icon,
        color: color,
        size: size,
      ),
    );
  }

  /// Creates an accessible image with proper semantics
  static Widget accessibleImage({
    required Widget image,
    required String semanticLabel,
    String? semanticHint,
    bool decorative = false,
  }) {
    if (decorative) {
      return ExcludeSemantics(child: image);
    }

    return Semantics(
      label: semanticLabel,
      hint: semanticHint,
      image: true,
      child: image,
    );
  }

  /// Creates an accessible form field with proper semantics
  static Widget accessibleFormField({
    required Widget child,
    required String label,
    String? hint,
    String? error,
    bool required = false,
  }) {
    return Semantics(
      label: label + (required ? ' (required)' : ''),
      hint: hint,
      textField: true,
      child: child,
    );
  }

  /// Announces a message to screen readers
  static void announceMessage(String message) {
    SemanticsService.announce(message, TextDirection.ltr);
  }

  /// Creates a focus trap for modal dialogs
  static Widget createFocusTrap({
    required Widget child,
    bool autofocus = true,
  }) {
    return Focus(
      autofocus: autofocus,
      child: child,
    );
  }

  /// Checks if high contrast mode is enabled
  static bool isHighContrastEnabled(BuildContext context) {
    return MediaQuery.of(context).highContrast;
  }

  /// Checks if animations should be reduced
  static bool shouldReduceAnimations(BuildContext context) {
    return MediaQuery.of(context).disableAnimations;
  }

  /// Gets the appropriate text scale factor
  static double getTextScaleFactor(BuildContext context) {
    return MediaQuery.of(context).textScaler.scale(1.0);
  }

  /// Creates an accessible card with proper semantics
  static Widget accessibleCard({
    required Widget child,
    required String semanticLabel,
    String? semanticHint,
    VoidCallback? onTap,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    Color? color,
    double? elevation,
    BorderRadius? borderRadius,
  }) {
    Widget card = Card(
      color: color,
      elevation: elevation,
      margin: margin,
      shape: borderRadius != null
          ? RoundedRectangleBorder(borderRadius: borderRadius)
          : null,
      child: Padding(
        padding: padding ?? const EdgeInsets.all(16.0),
        child: child,
      ),
    );

    if (onTap != null) {
      card = InkWell(
        onTap: onTap,
        borderRadius: borderRadius,
        child: card,
      );
    }

    return Semantics(
      label: semanticLabel,
      hint: semanticHint,
      button: onTap != null,
      child: card,
    );
  }

  /// Creates an accessible list item with proper semantics
  static Widget accessibleListItem({
    required Widget child,
    required String semanticLabel,
    String? semanticHint,
    VoidCallback? onTap,
    bool selected = false,
    int? index,
    int? totalItems,
  }) {
    String fullLabel = semanticLabel;
    if (index != null && totalItems != null) {
      fullLabel += ' ${index + 1} of $totalItems';
    }
    if (selected) {
      fullLabel += ', selected';
    }

    return Semantics(
      label: fullLabel,
      hint: semanticHint,
      button: onTap != null,
      selected: selected,
      child: child,
    );
  }

  /// Creates an accessible loading indicator
  static Widget accessibleLoadingIndicator({
    String label = 'Loading',
    String? hint,
    Widget? child,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      liveRegion: true,
      child: child ?? const CircularProgressIndicator(),
    );
  }
}

/// Extension methods for easier accessibility implementation
extension AccessibilityExtensions on Widget {
  /// Adds semantic label to any widget
  Widget withSemantics({
    String? label,
    String? hint,
    bool? button,
    bool? header,
    bool? image,
    bool? textField,
    bool? selected,
    bool? enabled,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      button: button,
      header: header,
      image: image,
      textField: textField,
      selected: selected,
      enabled: enabled,
      child: this,
    );
  }

  /// Excludes widget from semantics tree
  Widget excludeFromSemantics() {
    return ExcludeSemantics(child: this);
  }

  /// Ensures minimum touch target size
  Widget withMinTouchTarget({double minSize = 48.0}) {
    return ConstrainedBox(
      constraints: BoxConstraints(
        minWidth: minSize,
        minHeight: minSize,
      ),
      child: this,
    );
  }
}
