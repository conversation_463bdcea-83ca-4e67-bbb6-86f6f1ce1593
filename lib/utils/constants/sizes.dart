
class AppSizes {
  // Padding and margin sizes
  static const double xs = 4;
  static const double xs2=6;
  static const double sm = 8;
  static const double sm2 = 12;
  static const double md = 16;
  static const double md2 = 20;
  static const double lg = 24;
  static const double xl = 32;

  // Default spacing between sections
  static const double spaceExtraSmall = 4;
  static const double spaceSmall = 8;
  static const double spaceBtwList = 12;
  static const double spaceBtwItems = 16;
  static const double defaultSpace = 24;
  static const double spaceBtwSections = 32;

  // Input field
  static const double inputFieldRadius = 8;
  static const double inputFieldRadius2 = 12;
  static const double spaceBtwInputFields = 16;

  // Container Radius
  // Input field
  static const double containerRadius = 8;
  static const double containerRadius2 = 12;

  // Icon sizes
  static const double iconXs = 12;
  static const double iconSm = 16;
  static const double iconSm2 = 20;
  static const double iconMd = 24;
  static const double iconLg = 32;
  static const double iconXl = 36;

  // Font sizes
  static const double fontSizeSm = 14;
  static const double fontSizeMd = 16;
  static const double fontSizeLg = 18;

  // Button sizes - Enhanced for accessibility
  static const double buttonPadding = 8;
  static const double buttonRadius = 16;
  static const double buttonWidth = 120;
  static const double buttonElevation = 4;

  // Touch target sizes for accessibility compliance
  static const double minTouchTarget = 48;
  static const double recommendedTouchTarget = 56;
  static const double largeTouchTarget = 64;

  // AppBar height
  static const double appBarHeight = 56;

  // Image sizes
  static const double imageThumbSize = 80;

  // Border radius
  static const double borderRadiusSm = 8;
  static const double borderRadiusMd = 12;
  static const double borderRadiusLg = 16;

  // Divider height
  static const double dividerHeight = 1;

  // Card sizes
  static const double cardElevation = 2;
  static const double cardRadiusXs = 6;
  static const double cardRadiusSm = 10;
  static const double cardRadiusMd = 12;
  static const double cardRadiusLg = 16;

  // Image carousel height
  static const double imageCarouselHeight = 200;

  // Loading indicator size
  static const double loadingIndicatorSize = 36;

  // Grid view spacing
  static const double gridViewSpacing = 16;
}



