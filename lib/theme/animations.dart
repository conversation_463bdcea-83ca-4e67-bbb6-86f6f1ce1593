import 'package:flutter/material.dart';

/// M3 Expressive Animation Constants and Utilities
/// Following Material Design 3 motion principles for enhanced user experience
class AppAnimations {
  AppAnimations._();

  // Duration constants following M3 motion guidelines
  static const Duration extraShort = Duration(milliseconds: 50);
  static const Duration short1 = Duration(milliseconds: 100);
  static const Duration short2 = Duration(milliseconds: 150);
  static const Duration short3 = Duration(milliseconds: 200);
  static const Duration short4 = Duration(milliseconds: 250);
  static const Duration medium1 = Duration(milliseconds: 300);
  static const Duration medium2 = Duration(milliseconds: 350);
  static const Duration medium3 = Duration(milliseconds: 400);
  static const Duration medium4 = Duration(milliseconds: 450);
  static const Duration long1 = Duration(milliseconds: 500);
  static const Duration long2 = Duration(milliseconds: 600);
  static const Duration long3 = Duration(milliseconds: 700);
  static const Duration long4 = Duration(milliseconds: 800);
  static const Duration extraLong1 = Duration(milliseconds: 900);
  static const Duration extraLong2 = Duration(milliseconds: 1000);

  // M3 Easing curves for expressive motion
  static const Curve emphasized = Curves.easeInOutCubicEmphasized;
  static const Curve emphasizedDecelerate = Curves.easeOutCubic;
  static const Curve emphasizedAccelerate = Curves.easeInCubic;
  static const Curve standard = Curves.easeInOut;
  static const Curve standardDecelerate = Curves.easeOut;
  static const Curve standardAccelerate = Curves.easeIn;

  // Common animation configurations
  static const Duration defaultDuration = medium2;
  static const Curve defaultCurve = emphasized;

  // Page transition animations
  static PageRouteBuilder<T> createRoute<T>({
    required Widget page,
    Duration duration = medium3,
    Curve curve = emphasized,
    RouteTransitionsBuilder? transitionsBuilder,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      transitionsBuilder: transitionsBuilder ?? _defaultTransition,
    );
  }

  static Widget _defaultTransition(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(1.0, 0.0),
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: emphasized,
      )),
      child: child,
    );
  }

  // Fade transition
  static Widget fadeTransition(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    return FadeTransition(
      opacity: CurvedAnimation(
        parent: animation,
        curve: emphasized,
      ),
      child: child,
    );
  }

  // Scale transition with bounce
  static Widget scaleTransition(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    return ScaleTransition(
      scale: CurvedAnimation(
        parent: animation,
        curve: Curves.elasticOut,
      ),
      child: child,
    );
  }

  // Slide up transition (for bottom sheets, modals)
  static Widget slideUpTransition(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(0.0, 1.0),
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: emphasized,
      )),
      child: child,
    );
  }
}

/// Enhanced AnimatedContainer with M3 motion principles
class M3AnimatedContainer extends StatelessWidget {
  final Widget child;
  final Duration duration;
  final Curve curve;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? color;
  final Decoration? decoration;
  final double? width;
  final double? height;
  final BoxConstraints? constraints;
  final AlignmentGeometry? alignment;

  const M3AnimatedContainer({
    super.key,
    required this.child,
    this.duration = AppAnimations.defaultDuration,
    this.curve = AppAnimations.defaultCurve,
    this.padding,
    this.margin,
    this.color,
    this.decoration,
    this.width,
    this.height,
    this.constraints,
    this.alignment,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: duration,
      curve: curve,
      padding: padding,
      margin: margin,
      color: color,
      decoration: decoration,
      width: width,
      height: height,
      constraints: constraints,
      alignment: alignment,
      child: child,
    );
  }
}

/// Enhanced button with micro-interactions
class M3InteractiveButton extends StatefulWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final Duration duration;
  final double scaleOnPress;
  final Color? splashColor;
  final BorderRadius? borderRadius;

  const M3InteractiveButton({
    super.key,
    required this.child,
    this.onPressed,
    this.duration = AppAnimations.short3,
    this.scaleOnPress = 0.95,
    this.splashColor,
    this.borderRadius,
  });

  @override
  State<M3InteractiveButton> createState() => _M3InteractiveButtonState();
}

class _M3InteractiveButtonState extends State<M3InteractiveButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: widget.scaleOnPress,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: AppAnimations.emphasized,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    _controller.forward();
  }

  void _onTapUp(TapUpDetails details) {
    _controller.reverse();
  }

  void _onTapCancel() {
    _controller.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      onTap: widget.onPressed,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: widget.child,
          );
        },
      ),
    );
  }
}

/// Staggered animation helper for lists
class StaggeredAnimationHelper {
  static Widget buildStaggeredAnimation({
    required int index,
    required Widget child,
    Duration delay = AppAnimations.extraShort,
    Duration duration = AppAnimations.medium2,
    Curve curve = AppAnimations.emphasized,
    Offset slideOffset = const Offset(0, 50),
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.0, end: 1.0),
      duration: duration + (delay * index),
      curve: curve,
      builder: (context, value, child) {
        return Transform.translate(
          offset: slideOffset * (1 - value),
          child: Opacity(
            opacity: value,
            child: child,
          ),
        );
      },
      child: child,
    );
  }
}
