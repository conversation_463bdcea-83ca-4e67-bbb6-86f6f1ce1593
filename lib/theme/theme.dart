import 'package:flutter/material.dart';
import 'package:platix/theme/widget_themes/appbar_theme.dart';
import 'package:platix/theme/widget_themes/bottom_sheet_theme.dart';
import 'package:platix/theme/widget_themes/checkbox_theme.dart';
import 'package:platix/theme/widget_themes/chip_theme.dart';
import 'package:platix/theme/widget_themes/dialog_theme.dart';
import 'package:platix/theme/widget_themes/elevated_button_theme.dart';
import 'package:platix/theme/widget_themes/outlined_button_theme.dart';
import 'package:platix/theme/widget_themes/progress_bar_theme.dart';
import 'package:platix/theme/widget_themes/text_field_theme.dart';
import 'package:platix/theme/widget_themes/text_selection_theme.dart';
import 'package:platix/theme/widget_themes/text_theme.dart';

import '../utils/constants/colors.dart';

class IAppTheme {
  IAppTheme._();

  static ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    fontFamily: 'Montserrat',
    colorScheme: ColorScheme.fromSeed(
      seedColor: AppColors.primary,
      brightness: Brightness.light,
      // Enhanced M3 Expressive colors with better contrast and vibrancy
      primary: AppColors.primary,
      secondary: AppColors.secondary,
      tertiary: AppColors.primary3,
      error: AppColors.red,
      surface: AppColors.background1,
      onSurface: AppColors.black,
      surfaceContainerHighest: AppColors.background2,
      outline: AppColors.borderSecondary,
      outlineVariant: AppColors.lightGrey,
    ),

    disabledColor: AppColors.buttonDisabled,
    brightness: Brightness.light,
    progressIndicatorTheme: IProgressBarTheme.lightProgressIndicatorTheme,
    textTheme: ITextTheme.lightTextTheme,
    chipTheme: IChipTheme.lightChipTheme,
    scaffoldBackgroundColor: AppColors.background1,
    appBarTheme: IAppBarTheme.lightAppBarTheme,
    checkboxTheme: ICheckboxTheme.lightCheckboxTheme,
    bottomSheetTheme: IBottomSheetTheme.lightBottomSheetTheme,
    elevatedButtonTheme: IElevatedButtonTheme.lightElevatedButtonTheme,
    outlinedButtonTheme: IOutlinedButtonTheme.lightOutlinedButtonTheme,
    inputDecorationTheme: ITextFormFieldTheme.lightInputDecorationTheme,
    textSelectionTheme: ITextSelection.lightTextSelectionTheme,
    dialogTheme: IDialogTheme.lightDialogTheme,
  );

  static ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    fontFamily: 'Montserrat',
    colorScheme: ColorScheme.fromSeed(
      seedColor: AppColors.primary,
      brightness: Brightness.dark,
      // Enhanced M3 Expressive dark colors with better contrast
      primary: AppColors.primary3,
      secondary: AppColors.secondary,
      tertiary: AppColors.primary4,
      error: AppColors.red,
      surface: AppColors.black,
      onSurface: AppColors.white,
      surfaceContainerHighest: AppColors.darkerGrey,
      outline: AppColors.darkGrey,
      outlineVariant: AppColors.grey,
    ),
    disabledColor: AppColors.grey,
    brightness: Brightness.dark,
    textTheme: ITextTheme.darkTextTheme,
    chipTheme: IChipTheme.darkChipTheme,
    scaffoldBackgroundColor: AppColors.black,
    appBarTheme: IAppBarTheme.darkAppBarTheme,
    checkboxTheme: ICheckboxTheme.darkCheckboxTheme,
    bottomSheetTheme: IBottomSheetTheme.darkBottomSheetTheme,
    elevatedButtonTheme: IElevatedButtonTheme.darkElevatedButtonTheme,
    outlinedButtonTheme: IOutlinedButtonTheme.darkOutlinedButtonTheme,
    inputDecorationTheme: ITextFormFieldTheme.darkInputDecorationTheme,
  );
}

ThemeData get theme => IAppTheme.lightTheme;
