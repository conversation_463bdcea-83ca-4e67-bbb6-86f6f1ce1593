import 'package:flutter/material.dart';
import 'package:platix/utils/app_export.dart';
import '../../utils/constants/colors.dart';

/// Custom Class for Light & Dark Text Themes
class ITextTheme {
  ITextTheme._(); // To avoid creating instances

  /// Enhanced M3 Expressive Light Text Theme
  static TextTheme lightTextTheme = TextTheme(
    // Display styles - for large, impactful text
    displayLarge: const TextStyle().copyWith(fontSize: 57, fontWeight: FontWeight.w400, color: AppColors.black, letterSpacing: -0.25, height: 1.12),
    displayMedium: const TextStyle().copyWith(fontSize: 45, fontWeight: FontWeight.w400, color: AppColors.black, letterSpacing: 0, height: 1.16),
    displaySmall: const TextStyle().copyWith(fontSize: 36, fontWeight: FontWeight.w400, color: AppColors.black, letterSpacing: 0, height: 1.22),

    // Headline styles - for high-emphasis text
    headlineLarge: const TextStyle().copyWith(fontSize: 32, fontWeight: FontWeight.w600, color: AppColors.black, letterSpacing: 0, height: 1.25),
    headlineMedium: const TextStyle().copyWith(fontSize: 28, fontWeight: FontWeight.w600, color: AppColors.black, letterSpacing: 0, height: 1.29),
    headlineSmall: const TextStyle().copyWith(fontSize: 24, fontWeight: FontWeight.w600, color: AppColors.black, letterSpacing: 0, height: 1.33),

    // Title styles - for medium-emphasis text
    titleLarge: const TextStyle().copyWith(fontSize: 22, fontWeight: FontWeight.w600, color: AppColors.black, letterSpacing: 0, height: 1.27),
    titleMedium: const TextStyle().copyWith(fontSize: 16, fontWeight: FontWeight.w600, color: AppColors.black, letterSpacing: 0.15, height: 1.50),
    titleSmall: const TextStyle().copyWith(fontSize: 14, fontWeight: FontWeight.w600, color: AppColors.black, letterSpacing: 0.1, height: 1.43),

    // Body styles - for regular text content
    bodyLarge: const TextStyle().copyWith(fontSize: 16, fontWeight: FontWeight.w400, color: AppColors.black, letterSpacing: 0.5, height: 1.50),
    bodyMedium: const TextStyle().copyWith(fontSize: 14, fontWeight: FontWeight.w400, color: AppColors.black, letterSpacing: 0.25, height: 1.43),
    bodySmall: const TextStyle().copyWith(fontSize: 12, fontWeight: FontWeight.w400, color: AppColors.black, letterSpacing: 0.4, height: 1.33),

    // Label styles - for supporting text
    labelLarge: const TextStyle().copyWith(fontSize: 14, fontWeight: FontWeight.w500, color: AppColors.black, letterSpacing: 0.1, height: 1.43),
    labelMedium: const TextStyle().copyWith(fontSize: 12, fontWeight: FontWeight.w500, color: AppColors.black, letterSpacing: 0.5, height: 1.33),
    labelSmall: const TextStyle().copyWith(fontSize: 11, fontWeight: FontWeight.w500, color: AppColors.black, letterSpacing: 0.5, height: 1.45),
  );

  /// Enhanced M3 Expressive Dark Text Theme
  static TextTheme darkTextTheme = TextTheme(
    // Display styles - for large, impactful text
    displayLarge: const TextStyle().copyWith(fontSize: 57, fontWeight: FontWeight.w400, color: AppColors.white, letterSpacing: -0.25, height: 1.12),
    displayMedium: const TextStyle().copyWith(fontSize: 45, fontWeight: FontWeight.w400, color: AppColors.white, letterSpacing: 0, height: 1.16),
    displaySmall: const TextStyle().copyWith(fontSize: 36, fontWeight: FontWeight.w400, color: AppColors.white, letterSpacing: 0, height: 1.22),

    // Headline styles - for high-emphasis text
    headlineLarge: const TextStyle().copyWith(fontSize: 32, fontWeight: FontWeight.w600, color: AppColors.white, letterSpacing: 0, height: 1.25),
    headlineMedium: const TextStyle().copyWith(fontSize: 28, fontWeight: FontWeight.w600, color: AppColors.white, letterSpacing: 0, height: 1.29),
    headlineSmall: const TextStyle().copyWith(fontSize: 24, fontWeight: FontWeight.w600, color: AppColors.white, letterSpacing: 0, height: 1.33),

    // Title styles - for medium-emphasis text
    titleLarge: const TextStyle().copyWith(fontSize: 22, fontWeight: FontWeight.w600, color: AppColors.white, letterSpacing: 0, height: 1.27),
    titleMedium: const TextStyle().copyWith(fontSize: 16, fontWeight: FontWeight.w600, color: AppColors.white, letterSpacing: 0.15, height: 1.50),
    titleSmall: const TextStyle().copyWith(fontSize: 14, fontWeight: FontWeight.w600, color: AppColors.white, letterSpacing: 0.1, height: 1.43),

    // Body styles - for regular text content
    bodyLarge: const TextStyle().copyWith(fontSize: 16, fontWeight: FontWeight.w400, color: AppColors.white, letterSpacing: 0.5, height: 1.50),
    bodyMedium: const TextStyle().copyWith(fontSize: 14, fontWeight: FontWeight.w400, color: AppColors.white, letterSpacing: 0.25, height: 1.43),
    bodySmall: const TextStyle().copyWith(fontSize: 12, fontWeight: FontWeight.w400, color: AppColors.white.withValues(alpha: 0.7), letterSpacing: 0.4, height: 1.33),

    // Label styles - for supporting text
    labelLarge: const TextStyle().copyWith(fontSize: 14, fontWeight: FontWeight.w500, color: AppColors.white, letterSpacing: 0.1, height: 1.43),
    labelMedium: const TextStyle().copyWith(fontSize: 12, fontWeight: FontWeight.w500, color: AppColors.white.withValues(alpha: 0.7), letterSpacing: 0.5, height: 1.33),
    labelSmall: const TextStyle().copyWith(fontSize: 11, fontWeight: FontWeight.w500, color: AppColors.white.withValues(alpha: 0.7), letterSpacing: 0.5, height: 1.45),
  );
}
