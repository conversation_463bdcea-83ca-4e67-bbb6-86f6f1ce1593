import 'package:flutter/material.dart';
import '../../utils/constants/colors.dart';
import '../../utils/constants/sizes.dart';

class IAppBarTheme {
  IAppBarTheme._();

  static const lightAppBarTheme = AppBarTheme(
    elevation: 0,
    centerTitle: true,
    scrolledUnderElevation: 3,
    backgroundColor: AppColors.primary,
    surfaceTintColor: AppColors.primary,
    shadowColor: AppColors.shadow1,

    iconTheme: IconThemeData(color: AppColors.white, size: AppSizes.iconMd),
    actionsIconTheme: IconThemeData(color: AppColors.white, size: AppSizes.iconMd),
    titleTextStyle: TextStyle(
        fontSize: 22.0, fontWeight: FontWeight.w600, color: AppColors.white, letterSpacing: 0),
  );

  static const darkAppBarTheme = AppBarTheme(
    elevation: 0,
    centerTitle: true,
    scrolledUnderElevation: 3,
    backgroundColor: AppColors.primary3,
    surfaceTintColor: AppColors.primary3,
    shadowColor: AppColors.shadow1,

    iconTheme: IconThemeData(color: AppColors.white, size: AppSizes.iconMd),
    actionsIconTheme: IconThemeData(color: AppColors.white, size: AppSizes.iconMd),
    titleTextStyle: TextStyle(
        fontSize: 22.0, fontWeight: FontWeight.w600, color: AppColors.white, letterSpacing: 0),
  );

}
