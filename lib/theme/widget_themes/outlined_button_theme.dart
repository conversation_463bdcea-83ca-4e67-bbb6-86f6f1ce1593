import 'package:flutter/material.dart';
import 'package:platix/utils/app_export.dart';

import '../../utils/constants/colors.dart';
import '../../utils/constants/sizes.dart';

/* -- Light & Dark Outlined Button Themes -- */
class IOutlinedButtonTheme {
  IOutlinedButtonTheme._(); //To avoid creating instances


  /* -- Enhanced M3 Expressive Light Theme -- */
  static final lightOutlinedButtonTheme  = OutlinedButtonThemeData(
    style: OutlinedButton.styleFrom(
      elevation: 0,
      foregroundColor: AppColors.primary,
      backgroundColor: Colors.transparent,
      side: const BorderSide(color: AppColors.borderPrimary, width: 1.5),
      textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600, letterSpacing: 0.1),
      padding: const EdgeInsets.symmetric(vertical: AppSizes.buttonPadding + 4, horizontal: AppSizes.md),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(AppSizes.buttonRadius + 4)),
    ).copyWith(
      // Enhanced M3 state interactions
      overlayColor: WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
        if (states.contains(WidgetState.pressed)) return AppColors.primary.withValues(alpha: 0.1);
        if (states.contains(WidgetState.hovered)) return AppColors.primary.withValues(alpha: 0.08);
        if (states.contains(WidgetState.focused)) return AppColors.primary.withValues(alpha: 0.1);
        return null;
      }),
      side: WidgetStateProperty.resolveWith<BorderSide>((Set<WidgetState> states) {
        if (states.contains(WidgetState.pressed)) return const BorderSide(color: AppColors.primary, width: 2);
        if (states.contains(WidgetState.focused)) return const BorderSide(color: AppColors.primary, width: 2);
        return const BorderSide(color: AppColors.borderPrimary, width: 1.5);
      }),
    ),
  );

  /* -- Enhanced M3 Expressive Dark Theme -- */
  static final darkOutlinedButtonTheme = OutlinedButtonThemeData(
    style: OutlinedButton.styleFrom(
      elevation: 0,
      foregroundColor: AppColors.primary3,
      backgroundColor: Colors.transparent,
      side: const BorderSide(color: AppColors.primary3, width: 1.5),
      textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600, letterSpacing: 0.1),
      padding: const EdgeInsets.symmetric(vertical: AppSizes.buttonPadding + 4, horizontal: AppSizes.md),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(AppSizes.buttonRadius + 4)),
    ).copyWith(
      // Enhanced M3 state interactions for dark theme
      overlayColor: WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
        if (states.contains(WidgetState.pressed)) return AppColors.primary3.withValues(alpha: 0.1);
        if (states.contains(WidgetState.hovered)) return AppColors.primary3.withValues(alpha: 0.08);
        if (states.contains(WidgetState.focused)) return AppColors.primary3.withValues(alpha: 0.1);
        return null;
      }),
      side: WidgetStateProperty.resolveWith<BorderSide>((Set<WidgetState> states) {
        if (states.contains(WidgetState.pressed)) return const BorderSide(color: AppColors.primary3, width: 2);
        if (states.contains(WidgetState.focused)) return const BorderSide(color: AppColors.primary3, width: 2);
        return const BorderSide(color: AppColors.primary3, width: 1.5);
      }),
    ),
  );
}
