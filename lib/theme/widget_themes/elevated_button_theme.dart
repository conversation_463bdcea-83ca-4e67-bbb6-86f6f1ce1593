import 'package:flutter/material.dart';
import 'package:platix/utils/app_export.dart';
import '../../utils/constants/colors.dart';
import '../../utils/constants/sizes.dart';

/* -- Light & Dark Elevated Button Themes -- */
class IElevatedButtonTheme {
  IElevatedButtonTheme._(); //To avoid creating instances


  /* -- Enhanced M3 Expressive Light Theme -- */
  static final lightElevatedButtonTheme  = ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      elevation: 1,
      shadowColor: AppColors.shadow1,
      surfaceTintColor: AppColors.primary,
      foregroundColor: AppColors.white,
      backgroundColor: AppColors.buttonPrimary,
      disabledForegroundColor: AppColors.white.withValues(alpha: 0.38),
      disabledBackgroundColor: AppColors.buttonDisabled,
      side: const BorderSide(color: Colors.transparent),
      padding: const EdgeInsets.symmetric(vertical: AppSizes.buttonPadding + 4, horizontal: AppSizes.md),
      textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600, letterSpacing: 0.1),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(AppSizes.buttonRadius + 4)),
    ).copyWith(
      // Enhanced M3 state interactions
      overlayColor: WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
        if (states.contains(WidgetState.pressed)) return AppColors.white.withValues(alpha: 0.1);
        if (states.contains(WidgetState.hovered)) return AppColors.white.withValues(alpha: 0.08);
        if (states.contains(WidgetState.focused)) return AppColors.white.withValues(alpha: 0.1);
        return null;
      }),
    ),
  );

  /* -- Enhanced M3 Expressive Dark Theme -- */
  static final darkElevatedButtonTheme = ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      elevation: 1,
      shadowColor: AppColors.shadow1,
      surfaceTintColor: AppColors.primary3,
      foregroundColor: AppColors.white,
      backgroundColor: AppColors.primary3,
      disabledForegroundColor: AppColors.darkGrey.withValues(alpha: 0.38),
      disabledBackgroundColor: AppColors.darkerGrey,
      side: const BorderSide(color: Colors.transparent),
      padding: const EdgeInsets.symmetric(vertical: AppSizes.buttonPadding + 4, horizontal: AppSizes.md),
      textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600, letterSpacing: 0.1),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(AppSizes.buttonRadius + 4)),
    ).copyWith(
      // Enhanced M3 state interactions for dark theme
      overlayColor: WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
        if (states.contains(WidgetState.pressed)) return AppColors.white.withValues(alpha: 0.1);
        if (states.contains(WidgetState.hovered)) return AppColors.white.withValues(alpha: 0.08);
        if (states.contains(WidgetState.focused)) return AppColors.white.withValues(alpha: 0.1);
        return null;
      }),
    ),
  );
}
